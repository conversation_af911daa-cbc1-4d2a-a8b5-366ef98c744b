using Microsoft.Extensions.Options;
using Nest;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Documents;
using RealPlusNLP.Api.Configuration.Options;

namespace RealPlusNLP.Api.Infrastructure.Services;

public class ElasticsearchService(
    IElasticClient client,
    IOptions<ElasticsearchOptions> options)
    : IElasticsearchService
{
    private readonly IElasticClient _client = client;
    private readonly ElasticsearchOptions _options = options.Value;

    public async Task<ISearchResponse<PropertyDocument>> SearchAsync(
        string query,
        CancellationToken cancellationToken = default)
    {
        var response = await _client.SearchAsync<PropertyDocument>(s => s
            .Index($"resource-{_options.BuildingIndexName}")
            .Query(q => q
                .QueryString(qs => qs
                    .Query(query)
                    .Fields(f => f
                        .Field(p => p.BuildingName)))
            .Size(1000)
            .TrackTotalHits(true)
            .TrackScores(true)
            .CancellationToken(cancellationToken));

        return response;
    }
}
